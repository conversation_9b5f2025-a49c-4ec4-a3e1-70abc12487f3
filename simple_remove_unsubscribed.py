#!/usr/bin/env python3
"""
Простой скрипт для удаления отписавшихся пользователей из базы рассылок
"""

import csv
import shutil
from datetime import datetime

def main():
    print("Удаление отписавшихся пользователей из базы рассылок...")
    
    # Читаем отписавшихся пользователей
    unsubscribed_emails = set()
    
    try:
        with open('unsubscribed_users.csv', 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                email = row['email'].strip().lower()
                unsubscribed_emails.add(email)
        
        print(f"Загружено {len(unsubscribed_emails)} отписавшихся email адресов")
    
    except FileNotFoundError:
        print("❌ Файл unsubscribed_users.csv не найден!")
        return
    
    # Создаем резервную копию recipients.csv
    backup_file = f"recipients.csv.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy2('recipients.csv', backup_file)
        print(f"✓ Создана резервная копия: {backup_file}")
    except FileNotFoundError:
        print("❌ Файл recipients.csv не найден!")
        return
    
    # Читаем recipients.csv
    original_emails = []
    kept_emails = []
    removed_count = 0
    
    try:
        with open('recipients.csv', 'r', encoding='utf-8') as file:
            for line in file:
                email = line.strip()
                if email:
                    original_emails.append(email)
                    
                    # Проверяем, есть ли этот email в списке отписавшихся
                    if email.lower() in unsubscribed_emails:
                        removed_count += 1
                        print(f"  Удаляем: {email}")
                    else:
                        kept_emails.append(email)
    
    except FileNotFoundError:
        print("❌ Файл recipients.csv не найден!")
        return
    
    # Записываем обновленный файл
    with open('recipients.csv', 'w', encoding='utf-8') as file:
        for email in kept_emails:
            file.write(email + '\n')
    
    # Отчет
    print("\n" + "="*50)
    print("ОТЧЕТ")
    print("="*50)
    print(f"Исходное количество адресов: {len(original_emails)}")
    print(f"Удалено адресов: {removed_count}")
    print(f"Осталось адресов: {len(kept_emails)}")
    
    if len(original_emails) > 0:
        print(f"Процент удаленных: {removed_count/len(original_emails)*100:.1f}%")
    
    print(f"\n✅ Готово! Файл recipients.csv обновлен")
    print(f"📁 Резервная копия: {backup_file}")

if __name__ == "__main__":
    main()

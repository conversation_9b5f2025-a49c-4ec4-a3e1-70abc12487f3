#!/bin/bash

# Скрипт для удаления отписавшихся пользователей из базы рассылок
# Использование: ./remove_unsubscribed.sh

echo "=================================================="
echo "УДАЛЕНИЕ ОТПИСАВШИХСЯ ПОЛЬЗОВАТЕЛЕЙ ИЗ РАССЫЛКИ"
echo "=================================================="

# Проверяем наличие файлов
if [ ! -f "unsubscribed_users.csv" ]; then
    echo "❌ Файл unsubscribed_users.csv не найден!"
    exit 1
fi

if [ ! -f "recipients.csv" ]; then
    echo "❌ Файл recipients.csv не найден!"
    exit 1
fi

# Создаем резервную копию
BACKUP_FILE="recipients.csv.backup_$(date +%Y%m%d_%H%M%S)"
cp recipients.csv "$BACKUP_FILE"
echo "✓ Создана резервная копия: $BACKUP_FILE"

# Извлекаем email адреса из unsubscribed_users.csv (пропускаем заголовок)
echo "✓ Извлекаем список отписавшихся пользователей..."
tail -n +2 unsubscribed_users.csv | cut -d',' -f2 | tr -d '"' > unsubscribed_emails.tmp

# Подсчитываем количество отписавшихся
UNSUBSCRIBED_COUNT=$(wc -l < unsubscribed_emails.tmp)
echo "✓ Найдено $UNSUBSCRIBED_COUNT отписавшихся email адресов"

# Подсчитываем исходное количество адресов в recipients.csv
ORIGINAL_COUNT=$(wc -l < recipients.csv)
echo "✓ Исходное количество адресов в базе рассылок: $ORIGINAL_COUNT"

# Создаем временный файл для хранения результата
TEMP_FILE="recipients_temp.csv"

# Удаляем отписавшихся пользователей
echo "✓ Удаляем отписавшихся пользователей..."
grep -v -i -F -f unsubscribed_emails.tmp recipients.csv > "$TEMP_FILE"

# Заменяем исходный файл
mv "$TEMP_FILE" recipients.csv

# Подсчитываем результат
NEW_COUNT=$(wc -l < recipients.csv)
REMOVED_COUNT=$((ORIGINAL_COUNT - NEW_COUNT))

# Удаляем временный файл
rm unsubscribed_emails.tmp

# Выводим отчет
echo ""
echo "=================================================="
echo "ОТЧЕТ"
echo "=================================================="
echo "Исходное количество адресов: $ORIGINAL_COUNT"
echo "Удалено адресов: $REMOVED_COUNT"
echo "Осталось адресов: $NEW_COUNT"

if [ $ORIGINAL_COUNT -gt 0 ]; then
    PERCENT=$(echo "scale=1; $REMOVED_COUNT * 100 / $ORIGINAL_COUNT" | bc -l 2>/dev/null || echo "N/A")
    echo "Процент удаленных: $PERCENT%"
fi

echo ""
echo "✅ Готово! Файл recipients.csv обновлен"
echo "📁 Резервная копия сохранена как: $BACKUP_FILE"
echo ""
echo "Для восстановления исходного файла выполните:"
echo "cp $BACKUP_FILE recipients.csv"

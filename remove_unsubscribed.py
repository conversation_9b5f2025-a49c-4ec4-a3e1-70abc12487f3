#!/usr/bin/env python3
"""
Скрипт для удаления отписавшихся пользователей из базы рассылок
Читает список отписавшихся из unsubscribed_users.csv и удаляет их из recipients.csv
"""

import csv
import os
import shutil
from datetime import datetime

def load_unsubscribed_emails(unsubscribed_file):
    """
    Загружает список email адресов отписавшихся пользователей
    """
    unsubscribed_emails = set()
    
    try:
        with open(unsubscribed_file, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                email = row['email'].strip().lower()
                if email:
                    unsubscribed_emails.add(email)
        
        print(f"✓ Загружено {len(unsubscribed_emails)} отписавшихся email адресов")
        return unsubscribed_emails
    
    except FileNotFoundError:
        print(f"❌ Файл {unsubscribed_file} не найден!")
        return set()
    except Exception as e:
        print(f"❌ Ошибка при чтении файла {unsubscribed_file}: {e}")
        return set()

def process_recipients(recipients_file, unsubscribed_emails):
    """
    Обрабатывает файл recipients.csv и удаляет отписавшихся пользователей
    """
    if not os.path.exists(recipients_file):
        print(f"❌ Файл {recipients_file} не найден!")
        return
    
    # Создаем резервную копию
    backup_file = f"{recipients_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(recipients_file, backup_file)
    print(f"✓ Создана резервная копия: {backup_file}")
    
    # Читаем исходный файл
    original_emails = []
    removed_emails = []
    kept_emails = []
    
    try:
        with open(recipients_file, 'r', encoding='utf-8') as file:
            # Проверяем, есть ли заголовки
            first_line = file.readline().strip()
            file.seek(0)
            
            # Если первая строка содержит @ - это email, иначе это заголовок
            has_header = '@' not in first_line
            
            if has_header:
                reader = csv.DictReader(file)
                email_column = None
                
                # Определяем название колонки с email
                fieldnames = reader.fieldnames
                for field in fieldnames:
                    if 'email' in field.lower() or 'mail' in field.lower():
                        email_column = field
                        break
                
                if not email_column:
                    email_column = fieldnames[0]  # Берем первую колонку
                
                print(f"✓ Обнаружен заголовок, используем колонку: {email_column}")
                
                for row in reader:
                    email = row[email_column].strip().lower()
                    original_emails.append(row[email_column].strip())  # Сохраняем оригинальный регистр
                    
                    if email in unsubscribed_emails:
                        removed_emails.append(email)
                    else:
                        kept_emails.append(row[email_column].strip())
            else:
                # Файл без заголовков, каждая строка - это email
                for line in file:
                    email = line.strip()
                    if email:
                        original_emails.append(email)
                        email_lower = email.lower()
                        
                        if email_lower in unsubscribed_emails:
                            removed_emails.append(email_lower)
                        else:
                            kept_emails.append(email)
        
        print(f"✓ Прочитано {len(original_emails)} email адресов из базы рассылок")
        print(f"✓ Найдено {len(removed_emails)} адресов для удаления")
        print(f"✓ Останется {len(kept_emails)} активных адресов")
        
        # Записываем обновленный файл
        with open(recipients_file, 'w', encoding='utf-8', newline='') as file:
            if has_header:
                writer = csv.writer(file)
                writer.writerow([email_column])
                for email in kept_emails:
                    writer.writerow([email])
            else:
                for email in kept_emails:
                    file.write(email + '\n')
        
        # Выводим отчет
        print("\n" + "="*60)
        print("ОТЧЕТ ОБ УДАЛЕНИИ")
        print("="*60)
        print(f"Исходное количество адресов: {len(original_emails)}")
        print(f"Удалено адресов: {len(removed_emails)}")
        print(f"Осталось адресов: {len(kept_emails)}")
        print(f"Процент удаленных: {len(removed_emails)/len(original_emails)*100:.1f}%")
        
        if removed_emails:
            print(f"\nУдаленные адреса:")
            for email in sorted(removed_emails):
                print(f"  - {email}")
        
        print(f"\n✓ Файл {recipients_file} успешно обновлен")
        print(f"✓ Резервная копия сохранена как: {backup_file}")
        
    except Exception as e:
        print(f"❌ Ошибка при обработке файла {recipients_file}: {e}")

def main():
    """
    Основная функция
    """
    print("="*60)
    print("УДАЛЕНИЕ ОТПИСАВШИХСЯ ПОЛЬЗОВАТЕЛЕЙ ИЗ БАЗЫ РАССЫЛОК")
    print("="*60)
    
    # Пути к файлам
    unsubscribed_file = "unsubscribed_users.csv"
    recipients_file = "recipients.csv"
    
    print(f"Файл с отписавшимися: {unsubscribed_file}")
    print(f"База рассылок: {recipients_file}")
    print()
    
    # Загружаем список отписавшихся
    unsubscribed_emails = load_unsubscribed_emails(unsubscribed_file)
    
    if not unsubscribed_emails:
        print("❌ Нет данных об отписавшихся пользователях. Завершение работы.")
        return
    
    # Обрабатываем базу рассылок
    process_recipients(recipients_file, unsubscribed_emails)
    
    print("\n✅ Обработка завершена!")

if __name__ == "__main__":
    main()
